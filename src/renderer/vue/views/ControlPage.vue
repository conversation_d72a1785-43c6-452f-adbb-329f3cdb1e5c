<template>
  <div class="page">
    <div class="content-panel">
      <div v-if="hasLoaded && resources.length === 0" class="empty-message">
        <router-link to="/solutions" class="add-solution-link">
          <img src="@assets/svg/add_img_btn.svg" alt="add-btn" />
        </router-link>
        <span>当前没有播控方案，请先添加播控方案</span>
      </div>
      <div v-else class="main-layout">
        <!-- 上部分：控制区域 -->
        <div class="control-layout">
          <!-- 左侧资源面板 -->
          <div class="resource-panel">
            <div class="panel-header">
              <div class="panel-actions">
                <!-- 插入透明字符 -->
                <div class="filter-label">
                  类<span style="visibility: hidden">类型</span>型
                </div>
                <div class="filter-select">
                  <el-select v-model="typeFilter">
                    <el-option value="all" label="全部"></el-option>
                    <el-option
                      v-for="(label, type) in mainTypeOptions"
                      :key="type"
                      :value="type"
                      :label="label"
                    >
                    </el-option>
                  </el-select>
                </div>

                <div class="filter-label">一级分类</div>
                <div class="filter-select">
                  <el-select v-model="groupFilter">
                    <el-option value="all" label="全部"></el-option>
                    <el-option
                      v-for="group in mainGroups"
                      :key="group"
                      :value="group"
                    >
                      {{ group }}
                    </el-option>
                  </el-select>
                </div>

                <div class="filter-label">二级分类</div>
                <div class="filter-select">
                  <el-select
                    v-model="subcategoryFilter"
                    class="filter-select"
                    :disabled="groupFilter === 'all'"
                  >
                    <el-option value="all" label="全部"></el-option>
                    <el-option
                      v-for="subcategory in filteredSubcategories"
                      :key="subcategory"
                      :value="subcategory"
                    >
                      {{ subcategory }}
                    </el-option>
                  </el-select>
                </div>
                <!-- <select v-model="sortOption" class="filter-select">
                  <option value="name">按名称排序</option>
                  <option value="date">按日期排序</option>
                  <option value="type">按类型排序</option>
                </select> -->
              </div>

              <div class="serach-bar">
                <el-input v-model="searchQuery" placeholder="搜索"
                  ><template #prefix>
                    <img
                      src="@assets/svg/control/search.svg"
                      alt="search"
                    /> </template
                ></el-input>
              </div>
            </div>
            <div class="panel-content">
              <!-- 资源列表 -->
              <div v-if="loading.resources" class="loading-indicator">
                <div class="spinner"></div>
                <p>加载中...</p>
              </div>
              <div
                v-else-if="filteredResources.length === 0"
                class="empty-message"
              >
                暂无相关资源
              </div>
              <div v-else class="resource-cards">
                <!-- 资源卡片 -->
                <ResourceCard
                  v-for="(resource, index) in filteredResources"
                  :key="resource.index || index"
                  :resource="resource"
                  :index="index"
                  mode="view"
                  :controls="['publish', 'edit', 'delete']"
                  :publish-records="publishRecords"
                  :show-preview="true"
                  @publish="handleResourcePublish"
                  @edit="handleResourceEdit"
                  @delete="handleResourceDelete"
                  @play-video="playVideo"
                  @preview-image="previewImage"
                  @edit-publish="editPlayRecord"
                  @stop-publish="stopPlayRecord"
                  @show-play-view="showBigPlayView"
                />
              </div>
            </div>
          </div>

          <!-- <button
            class="collapse-btn"
            :class="{ collapsed: isRecordPanelCollapsed }"
            @click.stop="toggleRecordPanel"
          >
            <i
              :class="
                isRecordPanelCollapsed
                  ? 'icon-chevron-left'
                  : 'icon-chevron-right'
              "
            ></i>
          </button> -->

          <div
            class="collapse-btn"
            :class="{ collapsed: isRecordPanelCollapsed }"
            @click.stop="toggleRecordPanel"
          ></div>

          <!-- 右侧记录面板 -->
          <div
            class="right-column"
            :class="{ collapsed: isRecordPanelCollapsed }"
          >
            <!-- 发布记录面板 -->
            <div class="record-panel">
              <PlayRecord
                :devices="devices"
                :resources="resources"
                :auto-refresh="false"
                @edit="editPlayRecord"
                @stop="stopPlayRecord"
                @delete="deletePlayRecord"
                @republish="republishPlayRecord"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑发布记录对话框 -->
    <EditRecordDialog
      :show="showEditDialog"
      :record="currentRecord"
      :resources="resources"
      :devices="addedDevices"
      @close="closeEditDialog"
      @save="handleRecordSave"
    />

    <!-- 设备选择对话框 -->
    <div v-if="showDeviceDialog" class="dialog-overlay">
      <div class="dialog-content">
        <div class="panel-header">
          <div class="panel-actions" @click="closeDeviceDialog">
            <img src="@assets/svg/close.svg" alt="" />
          </div>
          <div id="title">{{ isEditMode ? "编辑播控" : "创建播控" }}</div>
          <div
            class="btn-confirm"
            @click="confirmDeviceSelection"
            :class="{
              'btn-disabled':
                !controlName.trim() ||
                selectedDevices.length === 0 ||
                selectedResources.length === 0,
            }"
          >
            {{ isEditMode ? "发布" : "发布" }}
          </div>
        </div>
        <div class="panel-content">
          <!-- 播控名称输入 -->
          <div class="form-group">
            <div class="section-title">播控名称</div>
            <input
              type="text"
              v-model="controlName"
              placeholder="请输入播控名称"
              class="form-control"
            />
          </div>
          <DevicePanel
            v-model="selectedDevices"
            v-model:isGroupMode="isGroupMode"
            :devices="isEditMode ? editableDevices : addedDevices"
            :device-groups="deviceGroups"
            :loading="loading.devices || loading.groups"
            :disable-offline="true"
            :disabled-devices="
              isEditMode ? disabledDevicesInEdit : publishedDevices
            "
            :show-mode-toggle="true"
            :show-refresh-button="true"
            :auto-refresh="true"
            :refresh-interval="1000"
            :title="isEditMode ? '已选设备' : '设备列表'"
            empty-message="暂无已添加的设备"
            @select="onDeviceSelect"
            @deselect="onDeviceDeselect"
            @refresh="refreshDeviceStatus"
            style="margin: 0"
          />
        </div>

        <div>
          <div class="section-title">播放策略</div>
          <div class="control-actions">
            <label class="checkbox-label">
              <input type="checkbox" v-model="showPlayScreen" />
              <span>PC端预览播放画面</span>
            </label>
            <label
              v-if="
                isVideoType(selectedType) ||
                (isEditMode &&
                  currentRecord &&
                  currentRecord.resources &&
                  currentRecord.resources.length > 0 &&
                  resources.find(
                    (r) => r.index === currentRecord.resources[0]
                  ) &&
                  isVideoType(
                    resources.find(
                      (r) => r.index === currentRecord.resources[0]
                    ).type
                  ))
              "
              class="checkbox-label"
            >
              <input type="checkbox" v-model="loopPlay" />
              <span>视频循环播放</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用共享的媒体预览对话框组件 -->
    <MediaPreviewDialog
      :show="showMediaPreview"
      :type="mediaPreviewType"
      :media="currentMedia"
      :mediaUrl="mediaUrl"
      :fullscreen="isRecordPanelCollapsed"
      :showPreview="showPlayScreen"
      :isPublished="currentMedia ? currentMedia.isPublished : false"
      @close="closeMediaPreview"
      @error="handleMediaError"
      @stop-control="handleStopControl"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import ResourceCard from "../components/ResourceCard.vue";
import DevicePanel from "../components/DevicePanel.vue";
import PlayRecord from "../components/PlayRecord.vue";
import EditRecordDialog from "../components/EditRecordDialog.vue";
import MediaPreviewDialog from "../components/MediaPreviewDialog.vue";
import messageService from "../plugins/message";
import {
  getMainFileType,
  MainFileType,
  MainTypeLabels,
  FileType,
  isVideoType,
} from "../../../shared/types/resource.js";
import {
  isSubcategory,
  getMainCategory,
  getSubcategory,
  createFullGroupName,
} from "../utils/group-utils";

// 使用路由
const router = useRouter();

// 使用Electron API
const electronAPI = useElectronAPI();

// 状态
const devices = ref([]);
const resources = ref([]);
const hasLoaded = ref(false); // 初始未获取

const deviceGroups = ref([]); // 设备分组列表
const publishRecords = ref([]); // 添加发布记录状态
const loading = ref({
  devices: false,
  resources: false,
  groups: false,
});
const error = ref({
  devices: null,
  resources: null,
  groups: null,
});

// 筛选和搜索
const searchQuery = ref("");
const typeFilter = ref("all");
const groupFilter = ref("all");
const subcategoryFilter = ref("all");
const sortOption = ref("name");
const enableSubcategories = ref(true); // 启用子分类功能

// 模式切换
const isGroupMode = ref(false);

// 选择状态
const selectedDevices = ref([]);
const selectedResources = ref([]);

// 循环发布开关
const loopPlay = ref(false);

// 编辑对话框状态
const showEditDialog = ref(false);
const currentRecord = ref(null);

// 媒体预览相关状态
const showMediaPreview = ref(false);
const mediaPreviewType = ref("image"); // 'image' 或 'video'
const currentMedia = ref(null);
const mediaUrl = ref("");

// 设备选择对话框状态
const showDeviceDialog = ref(false);
const isEditMode = ref(false); // 添加编辑模式状态

// 播控名称
const controlName = ref("");

// 播控选项
const showPlayScreen = ref(true);

// 发布记录面板状态
const isRecordPanelCollapsed = ref(true);
const recordPanelAutoShow = ref(false);

// 计算属性 - 主类型选项
const mainTypeOptions = computed(() => {
  // 创建一个包含所有主类型的对象
  const options = {};

  // 添加每个主类型及其标签（排除OTHER类型）
  for (const type in MainFileType) {
    const mainType = MainFileType[type];
    if (mainType !== MainFileType.OTHER) {
      options[mainType] = MainTypeLabels[mainType];
    }
  }

  return options;
});

// 计算属性 - 主分组列表
const mainGroups = computed(() => {
  // 提取所有资源的主分组，并去重
  const groups = new Set();

  resources.value.forEach((resource) => {
    if (resource.groups && Array.isArray(resource.groups)) {
      resource.groups.forEach((group) => {
        if (group && typeof group === "string") {
          // 获取主分类
          const mainCategory = getMainCategory(group);
          groups.add(mainCategory);
        }
      });
    }
  });

  // 转换为数组并排序
  return Array.from(groups).sort();
});

// 计算属性 - 当前选中主分组下的子分类列表
const filteredSubcategories = computed(() => {
  if (groupFilter.value === "all") {
    return [];
  }

  // 提取当前选中主分组下的所有子分类，并去重
  const subcategories = new Set();

  resources.value.forEach((resource) => {
    if (resource.groups && Array.isArray(resource.groups)) {
      resource.groups.forEach((group) => {
        if (group && typeof group === "string" && isSubcategory(group)) {
          // 检查是否属于当前选中的主分组
          const mainCategory = getMainCategory(group);
          if (mainCategory === groupFilter.value) {
            const subCategory = getSubcategory(group);
            if (subCategory) {
              subcategories.add(subCategory);
            }
          }
        }
      });
    }
  });

  // 转换为数组并排序
  return Array.from(subcategories).sort();
});

// 资源分类列表已移除

// 计算属性 - 过滤后的资源
const filteredResources = computed(() => {
  return resources.value
    .filter((resource) => {
      // 类型过滤
      // 获取资源的主类型
      const resourceMainType = getMainFileType(resource.type);

      // 如果选择了特定类型，进行过滤
      if (typeFilter.value !== "all" && resourceMainType !== typeFilter.value) {
        return false;
      }

      // 分组过滤
      if (groupFilter.value !== "all") {
        if (enableSubcategories.value) {
          // 如果启用了子分类
          if (subcategoryFilter.value !== "all") {
            // 如果选择了特定子分类，检查资源是否属于该子分类
            const fullGroupName = createFullGroupName(
              groupFilter.value,
              subcategoryFilter.value
            );
            if (
              !resource.groups ||
              !Array.isArray(resource.groups) ||
              !resource.groups.includes(fullGroupName)
            ) {
              return false;
            }
          } else {
            // 如果选择了全部子分类，检查资源是否属于该主分组
            if (
              !resource.groups ||
              !Array.isArray(resource.groups) ||
              !resource.groups.some((group) => {
                const mainCategory = getMainCategory(group);
                return mainCategory === groupFilter.value;
              })
            ) {
              return false;
            }
          }
        } else {
          // 如果未启用子分类，使用原来的匹配方式
          if (
            !resource.groups ||
            !Array.isArray(resource.groups) ||
            !resource.groups.includes(groupFilter.value)
          ) {
            return false;
          }
        }
      }

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        return (
          (resource.showName || "").toLowerCase().includes(query) ||
          (resource.fileName || "").toLowerCase().includes(query) ||
          (resource.describe || "").toLowerCase().includes(query)
        );
      }

      return true;
    })
    .sort((a, b) => {
      // 首先按组排序
      if (groupFilter.value !== "all") {
        // 如果选择了特定组，该组的资源排在前面
        const aInGroup = a.groups && a.groups.includes(groupFilter.value);
        const bInGroup = b.groups && b.groups.includes(groupFilter.value);
        if (aInGroup !== bInGroup) {
          return aInGroup ? -1 : 1;
        }
      } else {
        // 如果没有选择组，按第一个组名排序
        const aGroup = (a.groups && a.groups[0]) || "";
        const bGroup = (b.groups && b.groups[0]) || "";
        if (aGroup !== bGroup) {
          return aGroup.localeCompare(bGroup);
        }
      }

      // 然后按 index 升序排序
      return (a.index || 0) - (b.index || 0);
    });
});

// 添加排序标志位防止递归
let isSorting = false;

// 排序函数（仅在数据变化时触发）
const sortingCardList = () => {
  if (isSorting) return;
  isSorting = true;

  // 创建新数组进行排序（不改变原数组）
  const sortedDevices = [...devices.value].sort((a, b) => {
    if (a.id != null && b.id != null) {
      return Number(a.id) - Number(b.id);
    }
    return String(a.id).localeCompare(String(b.id));
  });

  // 仅当顺序实际变化时更新
  if (JSON.stringify(sortedDevices) !== JSON.stringify(devices.value)) {
    devices.value = sortedDevices;
  }

  isSorting = false;
};

// 监听设备数据变化（添加 deep: true 确保对象内部变化也能捕获）
watch(devices, sortingCardList, { deep: true, immediate: true });

// 计算属性 - 已添加的设备
const addedDevices = computed(() => {
  return devices.value.filter((device) => device.isAdded === true);
});

// 计算属性 - 编辑模式下可选的设备
const editableDevices = computed(() => {
  if (!isEditMode.value || !currentRecord.value) {
    return addedDevices.value;
  }

  // 获取当前记录中的设备
  const currentDevices = currentRecord.value.devices || [];

  // 获取所有发布记录中的设备（包括当前记录）
  const allPublishedDevices = publishRecords.value.flatMap(
    (record) => record.devices || []
  );

  // 过滤设备列表，只包含：
  // 1. 当前记录中的设备
  // 2. 完全未被任何发布记录使用的设备
  return addedDevices.value.filter(
    (device) =>
      currentDevices.includes(device.sn) ||
      !allPublishedDevices.includes(device.sn)
  );
});

// 计算属性 - 是否可以播放
const canPlay = computed(() => {
  return selectedDevices.value.length > 0 && selectedResources.value.length > 0;
});

// 计算属性 - 获取所有发布记录中的设备（无论状态）
const publishedDevices = ref([]);

// 方法 - 获取所有发布记录中的设备
const fetchPublishedDevices = async () => {
  try {
    // 获取所有发布记录
    const publishRecords = await electronAPI.getPublishRecords();

    // 提取所有发布记录中的设备（无论状态）
    const devices = [];
    publishRecords.forEach((record) => {
      if (record.devices && Array.isArray(record.devices)) {
        record.devices.forEach((sn) => {
          if (!devices.includes(sn)) {
            devices.push(sn);
          }
        });
      }
    });

    // 更新发布设备列表
    publishedDevices.value = devices;
    // console.log("所有发布记录中的设备:", publishedDevices.value);
  } catch (error) {
    console.error("获取发布设备失败:", error);
  }
};

// 方法 - 获取设备和资源
const fetchDevices = async () => {
  try {
    loading.value.devices = true;
    error.value.devices = null;

    // 获取设备历史记录
    const historyDevices = await electronAPI.getDeviceHistory();
    console.log("ControlPage: 从API获取的设备历史:", historyDevices);

    if (!historyDevices || historyDevices.length === 0) {
      console.log("ControlPage: 没有历史设备");
      devices.value = [];
      loading.value.devices = false;
      return;
    }

    // 临时存储设备列表
    const deviceList = [];

    // 处理每个设备
    for (const device of historyDevices) {
      // 检查设备是否已添加
      const isAdded = await electronAPI.isDeviceAdded(device.sn);

      deviceList.push({
        sn: device.sn,
        id: device.id,
        isOnline: device.isOnline,
        isAdded: isAdded,
        status: device.status || {},
      });
    }

    // 更新设备列表
    devices.value = deviceList;

    console.log(
      "ControlPage: 设备列表加载完成，共",
      devices.value.length,
      "个设备，其中已添加",
      addedDevices.value.length,
      "个设备"
    );
  } catch (err) {
    error.value.devices = err.message || "获取设备列表失败";
    console.error("获取设备列表失败:", err);
  } finally {
    loading.value.devices = false;
  }
};

// 获取设备分组列表
const fetchDeviceGroups = async () => {
  try {
    loading.value.groups = true;
    error.value.groups = null;

    // 获取设备组列表
    const groups = await electronAPI.getDeviceGroups();
    deviceGroups.value = groups;
    console.log("ControlPage: 设备组列表加载完成，共", groups.length, "个组");
  } catch (err) {
    error.value.groups = err.message || "获取设备组列表失败";
    console.error("获取设备组列表失败:", err);
  } finally {
    loading.value.groups = false;
  }
};

const fetchResources = async () => {
  try {
    loading.value.resources = true;
    error.value.resources = null;

    // 获取资源列表
    let resourceList = await electronAPI.getResourceList();
    console.log("ControlPage: 从API获取的资源列表:", resourceList);

    if (!resourceList || resourceList.length === 0) {
      console.log("ControlPage: 没有资源");
      resources.value = [];
      loading.value.resources = false;
      return;
    }

    // 参考 ResourceView.js 中的解析方式
    // 检查资源列表是否是数组
    if (
      Array.isArray(resourceList) &&
      resourceList.length === 1 &&
      resourceList[0].list
    ) {
      // 如果是包含list属性的对象数组，取第一个对象的list作为资源列表
      console.log("ControlPage: 检测到资源列表包含在list属性中");
      resourceList = resourceList[0].list;
    }

    // 再次检查资源列表是否有效
    if (
      !resourceList ||
      !Array.isArray(resourceList) ||
      resourceList.length === 0
    ) {
      console.log("ControlPage: 解析后的资源列表为空");
      resources.value = [];
      loading.value.resources = false;
      return;
    }

    // 处理每个资源，确保它们有必要的属性
    const processedResources = resourceList.map((resource, index) => {
      // 如果资源没有index属性，添加一个
      if (resource.index === undefined) {
        resource.index = index;
      }

      // 确保资源有groups属性
      if (!resource.groups) {
        resource.groups = [];
      }

      // 移除模拟分类数据

      return resource;
    });

    // 更新资源列表
    // resources.value = processedResources;
    // 在需要更新资源列表的地方
    nextTick(() => {
      resources.value = processedResources;
    });

    console.log(
      "ControlPage: 资源列表加载完成，共",
      resources.value.length,
      "个资源"
    );
  } catch (err) {
    error.value.resources = err.message || "获取资源列表失败";
    console.error("获取资源列表失败:", err);
  } finally {
    loading.value.resources = false;
    hasLoaded.value = true; // 无论成功失败都标记为已加载
  }
};

// 方法 - 刷新资源
const refreshResources = () => {
  fetchResources();
};

// 方法 - 设备选择事件处理
const onDeviceSelect = (sn) => {
  console.log("设备选择:", sn);
};

const onDeviceDeselect = (sn) => {
  console.log("设备取消选择:", sn);
};

const deselectDevice = (sn) => {
  const index = selectedDevices.value.indexOf(sn);
  if (index !== -1) {
    selectedDevices.value.splice(index, 1);
  }
};

// 方法 - 资源选择（只支持单资源选中）
const toggleResourceSelection = (resource) => {
  const index = selectedResources.value.indexOf(resource.index);
  if (index === -1) {
    // 清空之前选中的资源
    selectedResources.value = [resource.index];
    // 清空已选设备列表
    selectedDevices.value = [];
  } else {
    // 取消选中
    selectedResources.value = [];
  }
};

const selectResource = (resource) => {
  // 清空之前选中的资源，只选中当前资源
  selectedResources.value = [resource.index];
  // 清空已选设备列表
  selectedDevices.value = [];
};

const deselectResource = (resource) => {
  // 清空选中的资源
  selectedResources.value = [];
};

const deselectResourceByIndex = (index) => {
  // 清空选中的资源
  selectedResources.value = [];
};

// 方法 - 获取设备名称
const getDeviceName = (sn) => {
  const device = devices.value.find((d) => d.sn === sn);
  return device ? device.id || device.sn : sn;
};

// 方法 - 获取资源名称
const getResourceName = (index) => {
  const resource = resources.value.find((r) => r.index === index);
  return resource
    ? resource.showName || resource.fileName || "未命名资源"
    : `资源 ${index}`;
};

// 方法 - 发布控制
const playResources = async (name) => {
  if (!canPlay.value) return;

  try {
    // 构建发布命令
    const resourceIndexes = selectedResources.value;
    const deviceSNs = selectedDevices.value;

    // 获取所有发布记录中的设备
    const allPublishedDevices = publishRecords.value
      .flatMap((record) => record.devices || [])
      .map((sn) => String(sn));

    // 过滤出未被其他发布记录使用的设备
    const availableDevices = deviceSNs.filter(
      (sn) => !allPublishedDevices.includes(String(sn))
    );

    if (availableDevices.length === 0) {
      alertService.alert({
        title: "发布失败",
        message: "所选设备已被其他发布记录使用，请选择其他设备",
        confirmButtonText: "确定",
      });
      return;
    }

    console.log("发布设置:", {
      availableDevices,
      resourceIndexes,
      loopPlay: loopPlay.value,
    });

    // 显示加载状态
    loading.value.resources = true;

    // 发送发布命令
    const successDevices = [];
    const failedDevices = [];

    for (const sn of availableDevices) {
      try {
        // 对每个资源发送发布命令
        for (const resourceIndex of resourceIndexes) {
          console.log("发送发布命令:", {
            resourceIndex: resourceIndex,
            deviceSN: sn,
            loopPlay: loopPlay.value,
          });

          // 使用 publishResource API，传递正确的参数
          await electronAPI.publishResource(
            resourceIndex,
            [sn],
            loopPlay.value
          );
        }
        successDevices.push(sn);
      } catch (err) {
        console.error(`设备 ${sn} 发布失败:`, err);
        failedDevices.push(sn);
      }
    }

    // 隐藏加载状态
    loading.value.resources = false;

    // 创建发布记录
    if (successDevices.length > 0) {
      try {
        // 创建发布记录数据
        const recordData = {
          name: name,
          resources: resourceIndexes.map((index) => Number(index)),
          devices: successDevices.map((sn) => String(sn)),
          loopPlay: Boolean(loopPlay.value),
          showPlayScreen: Boolean(showPlayScreen.value),
        };

        // 调用API创建发布记录
        const record = await electronAPI.createPublishRecord(recordData);
        console.log("发布记录创建成功:", record);

        // 更新资源的发布状态
        resources.value = resources.value.map((resource) => {
          if (resourceIndexes.includes(resource.index)) {
            return {
              ...resource,
              isPublished: true,
              publishRecordId: record.id,
            };
          }
          return resource;
        });

        // 触发发布记录更新事件，通知组件刷新
        window.dispatchEvent(new CustomEvent("publish-record-updated"));
      } catch (error) {
        console.error("创建发布记录失败:", error);
      }
    }

    // 显示结果
    if (failedDevices.length === 0) {
      messageService.success(
        `已成功向 ${successDevices.length} 个设备发送发布命令`
      );

      // 如果勾选了显示播放画面，并且有资源，则自动预览第一个资源
      if (showPlayScreen.value && resourceIndexes.length > 0) {
        const resourceIndex = resourceIndexes[0];
        const resource = resources.value.find((r) => r.index === resourceIndex);

        if (resource) {
          const mainType = getMainFileType(resource.type);
          if (mainType === MainFileType.VIDEO) {
            await previewMedia(resource, "video");
          } else if (mainType === MainFileType.IMAGE) {
            await previewMedia(resource, "image");
          }
        }
      }

      // 清空选择
      selectedResources.value = [];
      selectedDevices.value = [];
    } else {
      alertService.alert({
        title: "发布结果",
        message: `成功: ${successDevices.length} 个设备\n失败: ${failedDevices.length} 个设备`,
        confirmButtonText: "确定",
      });
    }
  } catch (error) {
    console.error("发布资源失败:", error);
    loading.value.resources = false;
    alertService.alert({
      title: "发布失败",
      message: `发送发布命令失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

const stopPlayback = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 确认对话框
    const confirmed = await alertService.confirm({
      title: "确认停止",
      message: `确定要停止 ${selectedDevices.value.length} 个设备的发布吗？`,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    if (!confirmed) return;

    // 显示加载状态
    loading.value.resources = true;

    // 发送停止命令
    const successDevices = [];
    const failedDevices = [];

    for (const sn of selectedDevices.value) {
      try {
        console.log("发送停止发布命令:", {
          deviceSN: sn,
        });

        // 使用 stopResource API，传递正确的参数
        // 确保传递的是简单的字符串数组
        // 由于我们不知道当前播放的是什么类型的资源，所以不传递资源类型和包名
        // 后端会自动检测设备状态并发送正确的停止命令
        await electronAPI.stopResource([String(sn)]);
        successDevices.push(sn);
      } catch (err) {
        console.error(`设备 ${sn} 停止发布失败:`, err);
        failedDevices.push(sn);
      }
    }

    // 隐藏加载状态
    loading.value.resources = false;

    // 显示结果
    if (failedDevices.length === 0) {
      alertService.alert({
        title: "停止成功",
        message: `已成功向 ${successDevices.length} 个设备发送停止发布命令`,
        confirmButtonText: "确定",
      });
    } else {
      alertService.alert({
        title: "停止结果",
        message: `成功: ${successDevices.length} 个设备\n失败: ${failedDevices.length} 个设备`,
        confirmButtonText: "确定",
      });
    }
  } catch (error) {
    console.error("停止发布失败:", error);
    loading.value.resources = false;
    alertService.alert({
      title: "停止失败",
      message: `发送停止发布命令失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 方法 - 创建发布任务
const createPlayTask = () => {
  // 导航到发布任务管理页面
  router.push("/play-tasks");
};

// 方法 - 编辑发布记录
const editPlayRecord = (record) => {
  console.log("[ControlPage] 编辑发布记录", record.id);
  console.log("[ControlPage] 记录详情:", JSON.stringify(record));

  // 使用新的编辑对话框
  showEditControl(record);
};

// 方法 - 关闭编辑对话框
const closeEditDialog = () => {
  console.log("[ControlPage] 关闭编辑对话框");
  showDeviceDialog.value = false;
  isEditMode.value = false;
  currentRecord.value = null;
};

// 方法 - 处理记录保存
const handleRecordSave = async (record) => {
  console.log("[ControlPage] 保存发布记录", record);

  try {
    // 显示加载状态
    loading.value.resources = true;

    // 1. 更新发布记录
    // 创建一个新的可序列化的记录对象
    const updateData = {
      id: record.id, // 添加id字段
      name: String(record.name),
      devices: (record.devices || []).map((sn) => String(sn)),
      resources: (record.resources || []).map((index) => Number(index)),
      loopPlay: Boolean(record.loopPlay),
      showPlayScreen: Boolean(record.showPlayScreen),
      status: String(record.status || "active"),
      updatedAt: new Date().toISOString(),
    };

    console.log("[ControlPage] 更新数据:", updateData);
    const updatedRecord = await electronAPI.updatePublishRecord(
      record.id,
      updateData
    );
    console.log("[ControlPage] 发布记录已更新:", updatedRecord);

    // 2. 重新发布资源到所有选中的设备
    const successDevices = [];
    const failedDevices = [];

    // 获取所有发布记录中的设备（除了当前记录）
    const otherPublishedDevices = publishRecords.value
      .filter((r) => r.id !== record.id)
      .flatMap((r) => r.devices || [])
      .map((sn) => String(sn));

    // 过滤出未被其他发布记录使用的设备
    const availableDevices = record.devices.filter(
      (sn) => !otherPublishedDevices.includes(String(sn))
    );

    if (availableDevices.length === 0) {
      messageService.error("所选设备已被其他发布记录使用，请选择其他设备");
      loading.value.resources = false;
      return;
    }

    for (const sn of availableDevices) {
      try {
        // 对每个资源发送发布命令
        for (const resourceIndex of record.resources) {
          // 获取资源信息
          const resource = resources.value.find(
            (r) => r.index === resourceIndex
          );
          const mainType = resource ? getMainFileType(resource.type) : null;

          // 使用 isVideoType 函数判断是否为视频资源
          const isVideo = resource && isVideoType(resource.type);

          // 只有视频资源才考虑循环播放设置
          const shouldLoopPlay = isVideo ? record.loopPlay : false;

          console.log("发送发布命令:", {
            resourceIndex: resourceIndex,
            deviceSN: sn,
            loopPlay: shouldLoopPlay,
            resourceType: mainType,
            isVideo: isVideo,
          });

          await electronAPI.publishResource(
            resourceIndex,
            [String(sn)],
            shouldLoopPlay
          );
        }
        successDevices.push(sn);
      } catch (err) {
        console.error(`设备 ${sn} 发布失败:`, err);
        failedDevices.push(sn);
      }
    }

    // 隐藏加载状态
    loading.value.resources = false;

    // 触发发布记录更新事件，通知组件刷新
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 如果勾选了显示播控画面，并且有资源，则自动预览第一个资源
    if (
      record.showPlayScreen &&
      record.resources &&
      record.resources.length > 0
    ) {
      // 获取第一个资源的详细信息
      const resourceIndex = record.resources[0];
      const resource = resources.value.find((r) => r.index === resourceIndex);

      if (resource) {
        // 根据资源类型调用相应的预览方法
        const mainType = getMainFileType(resource.type);
        if (mainType === MainFileType.VIDEO) {
          await previewMedia(resource, "video");
        } else if (mainType === MainFileType.IMAGE) {
          await previewMedia(resource, "image");
        }
      }
    }

    // 显示结果
    if (failedDevices.length === 0) {
      messageService.success(
        `播控更新成功，已重新发布到 ${successDevices.length} 个设备`
      );
    } else {
      messageService.warning(
        `播控更新成功，但部分设备发布失败\n成功: ${successDevices.length} 个设备\n失败: ${failedDevices.length} 个设备`
      );
    }
  } catch (error) {
    console.error("更新发布记录失败:", error);
    loading.value.resources = false;
    messageService.error(`更新失败: ${error.message || "未知错误"}`);
  }
};

// 方法 - 预览媒体（视频或图片）
const previewMedia = async (resource, type) => {
  try {
    // 应用类型优先显示封面图
    if (getMainFileType(resource.type) === MainFileType.APP) {
      let cover = resource.coverPath || resource.poster;
      if (cover) {
        // 确保路径正确拼接
        const path = resource.path.endsWith("/")
          ? `${resource.path}${cover}`
          : `${resource.path}/${cover}`;

        let filePath = path;
        try {
          if (window.electronAPI && window.electronAPI.getResourceFilePath) {
            filePath = await window.electronAPI.getResourceFilePath(path);
          }
        } catch (e) {
          // 忽略异常，直接用path
          console.log("获取封面路径失败，使用原始路径:", e);
        }
        currentMedia.value = resource;
        mediaPreviewType.value = "image";
        mediaUrl.value = filePath;
        showMediaPreview.value = true;
      } else {
        alertService.alert({
          title: "预览失败",
          message: "该应用没有封面图片，无法预览",
          type: "error",
        });
      }
      return;
    }
    console.log(
      `ControlPage: 预览${type === "video" ? "视频" : "图片"}被调用，资源:`,
      resource
    );

    // 设置当前媒体和类型
    currentMedia.value = resource;
    mediaPreviewType.value = type;

    // 获取媒体文件路径
    if (resource.path && resource.fileName) {
      const path = resource.path.endsWith("/")
        ? `${resource.path}${resource.fileName}`
        : `${resource.path}/${resource.fileName}`;

      console.log("ControlPage: 媒体路径:", path);

      try {
        // 使用 Electron API 获取本地文件路径
        if (window.electronAPI && window.electronAPI.getResourceFilePath) {
          const filePath = await window.electronAPI.getResourceFilePath(path);
          console.log("ControlPage: 获取到媒体路径:", filePath);
          mediaUrl.value = filePath;
        } else if (window.ipcRenderer) {
          const filePath = await window.ipcRenderer.invoke(
            "get-resource-file-path",
            path
          );
          console.log("ControlPage: 通过IPC获取到媒体路径:", filePath);
          mediaUrl.value = filePath;
        } else {
          // 如果没有 API，尝试直接使用路径
          console.log("ControlPage: 没有可用的API，直接使用路径");
          mediaUrl.value = path;
        }
      } catch (e) {
        // 如果 API 调用失败，尝试直接使用路径
        console.log("ControlPage: API调用失败，尝试直接使用路径", e);
        mediaUrl.value = path;
      }

      // 显示媒体预览器
      console.log("ControlPage: 显示媒体预览器");
      showMediaPreview.value = true;
    } else {
      console.error("ControlPage: 媒体资源路径不完整");
      alertService.alert({
        title: type === "video" ? "播放失败" : "预览失败",
        message: `无法获取${type === "video" ? "视频" : "图片"}文件路径`,
        type: "error",
      });
    }
  } catch (error) {
    console.error(
      `ControlPage: 预览${type === "video" ? "视频" : "图片"}失败:`,
      error
    );
    alertService.alert({
      title: type === "video" ? "播放失败" : "预览失败",
      message:
        `无法${type === "video" ? "播放视频" : "预览图片"}: ` +
        (error.message || "未知错误"),
      type: "error",
    });
  }
};

// 方法 - 播放视频（调用预览媒体方法）
const playVideo = (resource) => {
  previewMedia(resource, "video");
};

// 方法 - 预览图片（调用预览媒体方法）
const previewImage = (resource) => {
  previewMedia(resource, "image");
};

// 方法 - 关闭媒体预览器
const closeMediaPreview = () => {
  // 重置状态
  showMediaPreview.value = false;
  currentMedia.value = null;
  mediaUrl.value = "";

  // 在下一个 tick 中聚焦回资源列表
  nextTick(() => {
    // 尝试聚焦回资源列表区域
    const resourceGrid = document.querySelector(".resource-cards");
    if (resourceGrid) {
      resourceGrid.focus();
    }
  });
};

// 方法 - 处理媒体加载错误
const handleMediaError = (event) => {
  console.error(
    `${mediaPreviewType.value === "video" ? "视频" : "图片"}加载失败:`,
    event
  );
  alertService.alert({
    title: `${mediaPreviewType.value === "video" ? "播放" : "预览"}失败`,
    message: `无法加载${mediaPreviewType.value === "video" ? "视频" : "图片"}文件`,
    type: "error",
  });
  closeMediaPreview();
};

// 方法 - 删除发布记录
const deletePlayRecord = (record) => {
  console.log("[ControlPage] 删除发布记录", record.id);
  // 这里不需要额外的处理，因为 PlayRecord 组件已经处理了删除操作
  // 包括确认对话框、删除记录和显示结果
};

// 方法 - 再次发布记录
const republishPlayRecord = async (record) => {
  console.log("[ControlPage] 开始再次发布记录", record.id);
  try {
    // 显示加载状态
    loading.value.resources = true;
    console.log("[ControlPage] 设置加载状态为true");

    // 获取设备列表
    const deviceSNs = Array.isArray(record.devices)
      ? record.devices.map((sn) => String(sn))
      : [];

    // 获取资源列表
    const resourceIndexes = Array.isArray(record.resources)
      ? record.resources
      : [];

    console.log("[ControlPage] 准备再次发布资源", {
      deviceSNs,
      resourceIndexes,
    });

    // 发布资源到设备
    for (const resourceIndex of resourceIndexes) {
      await electronAPI.publishResource(
        resourceIndex,
        deviceSNs,
        record.loopPlay || false
      );
    }

    // 更新记录状态
    console.log("[ControlPage] 更新记录状态");
    const updatedRecord = await electronAPI.activatePublishRecord(record.id);
    console.log("[ControlPage] 发布记录已激活:", updatedRecord);

    // 触发发布记录更新事件，通知组件刷新
    console.log("[ControlPage] 触发发布记录更新事件");
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 隐藏加载状态
    loading.value.resources = false;
    console.log("[ControlPage] 设置加载状态为false");

    // 清空已选资源和已选设备列表
    selectedResources.value = [];
    selectedDevices.value = [];
    console.log("[ControlPage] 已清空已选资源和已选设备列表");

    // 不显示成功对话框，因为PlayRecord组件已经显示了一个
    console.log(
      "[ControlPage] 再次发布成功，但不显示对话框（由PlayRecord组件显示）"
    );
  } catch (error) {
    console.error("再次发布失败:", error);
    loading.value.resources = false;
    alertService.alert({
      title: "再次发布失败",
      message: `发送再次发布命令失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 方法 - 停止发布记录
const stopPlayRecord = async (record, resourceType, packageName) => {
  console.log(
    "[ControlPage] 开始停止发布记录",
    record,
    resourceType,
    packageName
  );
  try {
    // 显示加载状态
    loading.value.resources = true;
    console.log("[ControlPage] 设置加载状态为true");

    // 首先停止设备的发布
    // 确保传递的是简单的字符串数组
    const deviceSNs = Array.isArray(record.devices)
      ? record.devices.map((sn) => String(sn))
      : [];

    console.log("[ControlPage] 准备停止设备发布", deviceSNs);
    if (deviceSNs.length > 0) {
      // 传递资源类型
      // 后端会自动检测设备状态并发送正确的停止命令
      await electronAPI.stopResource(deviceSNs, resourceType, packageName);
      console.log("[ControlPage] 设备发布已停止");
    }

    // 然后删除发布记录
    console.log("[ControlPage] 删除发布记录");
    await electronAPI.deletePublishRecord(record.id);
    console.log("[ControlPage] 发布记录已删除");

    // 更新资源的发布状态
    resources.value = resources.value.map((resource) => {
      if (record.resources.includes(resource.index)) {
        return {
          ...resource,
          isPublished: false,
          publishRecordId: null,
        };
      }
      return resource;
    });

    // 触发发布记录更新事件，通知组件刷新
    console.log("[ControlPage] 触发发布记录更新事件");
    window.dispatchEvent(new CustomEvent("publish-record-updated"));

    // 隐藏加载状态
    loading.value.resources = false;
    console.log("[ControlPage] 设置加载状态为false");

    // 清空已选资源和已选设备列表
    selectedResources.value = [];
    selectedDevices.value = [];
    console.log("[ControlPage] 已清空已选资源和已选设备列表");

    // 不显示成功对话框，因为PlayRecord组件已经显示了一个
    console.log(
      "[ControlPage] 停止成功，但不显示对话框（由PlayRecord组件显示）"
    );
    closeMediaPreview();
  } catch (error) {
    console.error("停止发布失败:", error);
    loading.value.resources = false;
    alertService.alert({
      title: "停止失败",
      message: `发送停止发布命令失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 轻量级刷新设备状态（由DevicePanel的自动刷新调用）
const refreshDeviceStatus = async () => {
  try {
    // 如果正在加载，跳过刷新
    if (loading.value.devices) return;

    // 获取设备历史记录，但不设置loading状态
    const historyDevices = await electronAPI.getDeviceHistory();

    if (!historyDevices || historyDevices.length === 0) return;

    // 更新现有设备的状态，而不是替换整个列表
    for (const newDevice of historyDevices) {
      const index = devices.value.findIndex((d) => d.sn === newDevice.sn);

      if (index !== -1) {
        // 只更新在线状态和状态信息，保留其他属性
        devices.value[index] = {
          ...devices.value[index],
          isOnline: newDevice.isOnline,
          status: newDevice.status || {},
        };
      } else {
        // 如果是新设备，检查是否已添加
        const isAdded = await electronAPI.isDeviceAdded(newDevice.sn);

        // 添加到设备列表
        devices.value.push({
          sn: newDevice.sn,
          id: newDevice.id,
          isOnline: newDevice.isOnline,
          isAdded: isAdded,
          status: newDevice.status || {},
        });
      }
    }

    // 检查是否有设备已被移除
    const currentSNs = historyDevices.map((d) => d.sn);
    devices.value = devices.value.filter((d) => currentSNs.includes(d.sn));
  } catch (err) {
    console.error("刷新设备状态失败:", err);
  }
};

// 处理URL查询参数
const handleUrlParams = async () => {
  const query = router.currentRoute.value.query;

  // 如果有设备参数，自动选中该设备
  if (query.device) {
    console.log("从URL参数中获取设备:", query.device);

    // 等待设备列表加载完成
    if (devices.value.length === 0) {
      await fetchDevices();
    }

    // 查找设备
    const device = devices.value.find((d) => d.sn === query.device);
    if (device && device.isOnline) {
      // 选中设备
      if (!selectedDevices.value.includes(device.sn)) {
        selectedDevices.value.push(device.sn);
        console.log(`已自动选中设备: ${device.sn}`);
      }
    }
  }

  // 如果有action参数，执行相应操作
  if (query.action === "cast") {
    console.log("从URL参数中获取操作: cast");

    // 等待资源列表加载完成
    if (resources.value.length === 0) {
      await fetchResources();
    }

    // 自动聚焦到资源列表
    setTimeout(() => {
      const resourcePanel = document.querySelector(".resource-panel");
      if (resourcePanel) {
        resourcePanel.scrollIntoView({ behavior: "smooth" });

        // 尝试聚焦到搜索框
        const searchInput = resourcePanel.querySelector(".search-input");
        if (searchInput) {
          searchInput.focus();
        }
      }
    }, 500);
  }
};

// 监听 groupFilter 变化，重置子分类过滤器
watch(groupFilter, () => {
  nextTick(() => {
    subcategoryFilter.value = "all";
  });
});

// 生命周期钩子
onMounted(async () => {
  // 加载设备和资源
  await fetchDevices();
  await fetchResources();
  await fetchDeviceGroups();
  await fetchPublishRecords(); // 添加获取发布记录

  // 获取所有发布记录中的设备
  await fetchPublishedDevices();

  // 设置事件监听器
  setupEventListeners();

  // 处理URL参数
  await handleUrlParams();

  // 注意：自动刷新现在由DevicePanel组件处理
});

onUnmounted(() => {
  // 移除事件监听器
  removeEventListeners();

  // 注意：自动刷新停止现在由DevicePanel组件处理
});

// 设置事件监听器
const setupEventListeners = () => {
  // 监听设备连接事件
  electronAPI.onDeviceConnected(handleDeviceConnected);

  // 监听设备断开连接事件
  electronAPI.onDeviceDisconnected(handleDeviceDisconnected);

  // 监听设备状态更新事件
  electronAPI.onDeviceStatusUpdate(handleDeviceStatusUpdated);

  // 监听发布记录更新事件
  window.addEventListener("publish-record-updated", handlePublishRecordUpdate);

  // 保存事件处理函数的引用，以便在卸载时移除
  window._publishRecordUpdateHandler = handlePublishRecordUpdate;

  // console.log("ControlPage: 事件监听器设置完成");
};

// 移除事件监听器
const removeEventListeners = () => {
  // 移除设备连接事件监听器
  electronAPI.offDeviceConnected(handleDeviceConnected);

  // 移除设备断开连接事件监听器
  electronAPI.offDeviceDisconnected(handleDeviceDisconnected);

  // 移除设备状态更新事件监听器
  electronAPI.offDeviceStatusUpdate(handleDeviceStatusUpdated);

  // 移除发布记录更新事件监听器
  if (window._publishRecordUpdateHandler) {
    window.removeEventListener(
      "publish-record-updated",
      window._publishRecordUpdateHandler
    );
    delete window._publishRecordUpdateHandler;
  }

  console.log("ControlPage: 事件监听器已移除");
};

// 设备事件处理函数
const handleDeviceConnected = async (device) => {
  // console.log('设备连接:', device);

  // 检查设备是否已添加
  const isAdded = await electronAPI.isDeviceAdded(device.sn);

  // 查找设备是否已存在
  const index = devices.value.findIndex((d) => d.sn === device.sn);

  if (index !== -1) {
    // 更新现有设备
    devices.value[index] = {
      ...devices.value[index],
      isOnline: true,
      isAdded: isAdded,
      status: device.status || {},
    };
  } else {
    // 添加新设备
    devices.value.push({
      sn: device.sn,
      id: device.id,
      isOnline: true,
      isAdded: isAdded,
      status: device.status || {},
    });
  }
};

const handleDeviceDisconnected = (sn) => {
  console.log("设备断开连接:", sn);

  // 查找设备
  const index = devices.value.findIndex((d) => d.sn === sn);

  if (index !== -1) {
    // 更新设备状态为离线，但保持其他属性不变
    devices.value[index] = {
      ...devices.value[index],
      isOnline: false,
      status: {},
    };
  }
};

let isUpdatingDeviceStatus = false;

function handleDeviceStatusUpdated(deviceId, status) {
  if (isUpdatingDeviceStatus) return;
  const index = devices.value.findIndex((d) => d.id === deviceId);
  if (index !== -1 && devices.value[index].status !== status) {
    isUpdatingDeviceStatus = true;
    const newDevices = [...devices.value];
    newDevices[index] = { ...newDevices[index], status };
    devices.value = newDevices;
    isUpdatingDeviceStatus = false;
  }
}

// 监听发布记录更新事件
const handlePublishRecordUpdate = async () => {
  console.log("发布记录更新，重新获取所有发布记录中的设备");
  await fetchPublishRecords(); // 重新获取发布记录并更新资源状态
  await fetchPublishedDevices();

  // 显示发布记录面板
  isRecordPanelCollapsed.value = false;
};

// 切换发布记录面板
let isTogglingRecordPanel = false;

const toggleRecordPanel = () => {
  if (isTogglingRecordPanel) return;
  isTogglingRecordPanel = true;
  isRecordPanelCollapsed.value = !isRecordPanelCollapsed.value;
  setTimeout(() => {
    isTogglingRecordPanel = false;
  }, 0);
};

// 自动隐藏发布记录面板
const autoHideRecordPanel = () => {
  if (!recordPanelAutoShow.value) {
    setTimeout(() => {
      isRecordPanelCollapsed.value = true;
    }, 3000); // 3秒后自动隐藏
  }
};

const selectedType = ref(0);

// 方法 - 显示创建播控对话框
const showCreateControl = () => {
  // 重置编辑模式
  isEditMode.value = false;

  // 获取选中资源的名称作为播控名称
  if (selectedResources.value.length > 0) {
    const resource = resources.value.find(
      (r) => r.index === selectedResources.value[0]
    );
    controlName.value = resource
      ? resource.showName || resource.fileName || "未命名资源"
      : "";
    selectedType.value = resource ? resource.type : 0; // 设置默认类型为选中资源的类型
    console.log("🌈显示设备选择对话框，当前播控名称:", resource);
  }
  showPlayScreen.value = true; // 重置显示播放画面选项
  loopPlay.value = false; // 重置循环播放选项
  showDeviceDialog.value = true;
};

// 方法 - 显示编辑播控对话框
const showEditControl = (record) => {
  // 设置编辑模式
  isEditMode.value = true;

  // 设置当前记录
  currentRecord.value = record;

  // 设置播控名称
  controlName.value = record.name || "";

  // 设置选中的设备
  selectedDevices.value = record.devices || [];

  // 设置选中的资源
  selectedResources.value = record.resources || [];

  // 设置资源类型
  if (record.resources && record.resources.length > 0) {
    const resource = resources.value.find(
      (r) => r.index === record.resources[0]
    );
    selectedType.value = resource ? resource.type : 0;
  }

  // 设置播放选项
  showPlayScreen.value = record.showPlayScreen ?? true;
  loopPlay.value = record.loopPlay ?? false;

  // 显示对话框
  showDeviceDialog.value = true;
};

// 方法 - 关闭设备选择对话框
const closeDeviceDialog = () => {
  showDeviceDialog.value = false;
  isEditMode.value = false;
  currentRecord.value = null;
};

// 方法 - 确认设备选择
const confirmDeviceSelection = () => {
  if (!controlName.value.trim()) {
    alertService.alert({
      title: "提示",
      message: "请输入播控名称",
      confirmButtonText: "确定",
    });
    return;
  }

  if (selectedDevices.value.length === 0) {
    alertService.alert({
      title: "提示",
      message: "请选择要控制的设备",
      confirmButtonText: "确定",
    });
    return;
  }

  if (selectedResources.value.length === 0) {
    alertService.alert({
      title: "提示",
      message: "请选择要发布的资源",
      confirmButtonText: "确定",
    });
    return;
  }

  if (isEditMode.value) {
    // 编辑模式：更新发布记录
    if (!currentRecord.value || !currentRecord.value.id) {
      console.error("[ControlPage] 当前记录无效:", currentRecord.value);
      messageService.error("当前记录无效，无法保存");
      return;
    }

    // 创建一个新的记录对象，保留原始记录的id和createdAt
    const updatedRecord = {
      ...currentRecord.value,
      name: controlName.value,
      devices: selectedDevices.value,
      resources: selectedResources.value,
      showPlayScreen: showPlayScreen.value,
      loopPlay: loopPlay.value,
    };

    console.log("[ControlPage] 准备更新记录:", updatedRecord);
    handleRecordSave(updatedRecord);
  } else {
    // 创建模式：开始发布
    playResources(controlName.value);
  }

  // 关闭对话框
  closeDeviceDialog();
};

// 处理资源发布
const handleResourcePublish = (resource) => {
  // 设置当前资源
  selectedResources.value = [resource.index];
  // 显示设备选择对话框
  showCreateControl();
};

const showBigPlayView = async (resource) => {
  // 设置当前资源
  selectedResources.value = [resource.index];
  // 显示设备选择对话框
  showPlayScreen.value = true; // 重置显示播放画面选项

  const resourceIndexes = selectedResources.value;
  // 如果勾选了显示播放画面，并且有资源，则自动预览第一个资源
  if (showPlayScreen.value && resourceIndexes.length > 0) {
    const resourceIndex = resourceIndexes[0];
    const resource = resources.value.find((r) => r.index === resourceIndex);

    if (resource) {
      const mainType = getMainFileType(resource.type);
      if (mainType === MainFileType.VIDEO) {
        await previewMedia(resource, "video");
      } else if (mainType === MainFileType.IMAGE) {
        await previewMedia(resource, "image");
      }
    }
  }
};

// 处理资源编辑
const handleResourceEdit = (resource) => {
  // 实现资源编辑逻辑
};

// 处理资源删除
const handleResourceDelete = (resource) => {
  // 实现资源删除逻辑
};

// 处理资源编辑发布
const handleEditPublish = (resource) => {
  console.log("处理编辑发布:", resource);
  // 查找相关的发布记录
  const record = publishRecords.value.find((r) =>
    r.resources.includes(resource.index)
  );
  if (record) {
    editPlayRecord(record);
  } else {
    alertService.alert({
      title: "提示",
      message: "未找到相关的发布记录",
      confirmButtonText: "确定",
    });
  }
};

// 处理资源停止发布
const handleStopPublish = (resource) => {
  console.log("处理停止发布:", resource);
  // 查找相关的发布记录
  const record = publishRecords.value.find((r) =>
    r.resources.includes(resource.index)
  );
  if (record) {
    stopPlayRecord(record, resource.type, resource.pkg);
  } else {
    alertService.alert({
      title: "提示",
      message: "未找到相关的发布记录",
      confirmButtonText: "确定",
    });
  }
};

// 获取发布记录
const fetchPublishRecords = async () => {
  try {
    const records = await electronAPI.getPublishRecords();
    publishRecords.value = records;

    // 更新资源的发布状态
    resources.value = resources.value.map((resource) => {
      const record = records.find((r) => r.resources.includes(resource.index));
      return {
        ...resource,
        isPublished: !!record,
        publishRecordId: record?.id,
      };
    });
  } catch (error) {
    console.error("获取发布记录失败:", error);
  }
};

// 处理取消播控
const handleStopControl = async () => {
  console.log("🌈[ControlPage] 处理取消播控", currentMedia.value);

  if (!currentMedia.value) return;

  try {
    // 查找相关的发布记录
    const record = publishRecords.value.find((r) =>
      r.resources.includes(currentMedia.value.index)
    );

    if (record) {
      // 停止播控
      await stopPlayRecord(
        record,
        currentMedia.value.type,
        currentMedia.value.pkg
      );
    } else {
      alertService.alert({
        title: "提示",
        message: "未找到相关的发布记录",
        confirmButtonText: "确定",
      });
    }
  } catch (error) {
    console.error("停止播控失败:", error);
    alertService.alert({
      title: "停止失败",
      message: `停止播控失败: ${error.message || "未知错误"}`,
      confirmButtonText: "确定",
    });
  }
};

// 计算属性 - 编辑模式下已禁用的设备（当前记录中的设备）
const disabledDevicesInEdit = computed(() => {
  if (!isEditMode.value || !currentRecord.value) {
    return publishedDevices.value;
  }
  return currentRecord.value.devices || [];
});
</script>

<style scoped>
.page {
  padding: var(--spacing-md);
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  padding-right: 34px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
  padding-bottom: 4px;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.content-panel {
  position: relative;
  margin: 0 30px 26px 20px;
  width: calc(100% - var(--spacing-xs) * 2);
  box-sizing: border-box;
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

/* 主布局 */
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 控制布局 */
.control-layout {
  display: flex;
  height: 100%;
  gap: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 0;
  position: relative;
}

/* 右侧列布局 */
.right-column {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  transition: all 0.3s ease;
  transform: translateX(0);
  min-width: 0;
}

.right-column.collapsed {
  width: 0;
  transform: translateX(100%);
  margin-left: -16px;
  flex-shrink: 0;
}

.collapse-btn {
  position: absolute;
  right: 300px;
  top: 90px;
  transform: translateY(-50%);
  width: 40px;
  height: 52px;
  cursor: pointer;
  z-index: 101;

  background-image: url("../../assets/svg/collapsed.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain; /* 或其他合适的值 */
}

.collapse-btn.collapsed {
  right: 0;
}

.record-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  padding: 25px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin: 24px 0;
  #title {
    color: var(--color-menu-text);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px; /* 137.5% */
  }
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* 面板通用样式 */
.device-panel,
.resource-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  min-width: 0;
}

/* 面板特殊样式 */
.resource-panel {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-background);
  overflow: hidden;
  height: 100%;
  transition: all 0.3s ease;
}

.device-panel {
  flex: 1;
  overflow: hidden;
}

.panel-content {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  min-width: 0;
}

/* 设备和控制面板内容特殊样式 */
.device-panel .panel-content,
.control-panel .panel-content {
  overflow: hidden; /* 完全禁止滚动条出现 */
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保内容区域占满整个面板高度 */
  justify-content: space-between; /* 使内容分布在顶部和底部 */
}

/* 资源面板内容特殊样式 */
.resource-panel .panel-content {
  overflow-y: auto;
  overflow-x: hidden; /* 隐藏水平滚动条 */
  display: block; /* 使用块级布局而不是flex布局 */
  height: 100%; /* 确保内容区域占满面板高度 */
}

/* 自定义滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
  height: 0; /* 水平滚动条高度设为0，即不显示 */
}

.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 循环播放开关样式 */
.loop-play-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
}

.loop-play-switch span {
  margin-right: 8px;
}

.loop-play-switch input {
  display: none;
}

.loop-play-switch .slider {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
  background-color: #ccc;
  border-radius: 10px;
  transition: 0.4s;
}

.loop-play-switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: var(--color-card-background);
  border-radius: 50%;
  transition: 0.4s;
}

.loop-play-switch input:checked + .slider {
  background-color: var(--color-primary);
}

.loop-play-switch input:checked + .slider:before {
  transform: translateX(16px);
}

/* 只在悬停时显示滚动条，且只显示垂直滚动条 */
.panel-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  overflow-x: hidden; /* 确保所有浏览器都不显示水平滚动条 */
}

/* 设备列表样式 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.panel-actions {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.panel-actions img {
  width: 24px;
  height: 24px;
  padding: 5px;
}

.btn-confirm {
  color: var(--color-primary);
  background-color: white;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 16px;
  border-radius: var(--border-radius-sm);
}
.btn-confirm:hover {
  background-color: var(--color-card-background-online);
}
.btn-confirm:disabled {
  color: var(--color-text-secondary);
  background-color: var(--color-card-background);
  cursor: not-allowed;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.select-all-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: var(--color-card-background);
  border: 2px solid var(--color-border);
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加轻微阴影增强立体感 */
}

.select-all-checkbox input[type="checkbox"]:checked ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.select-all-checkbox input[type="checkbox"]:checked ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid var(--color-white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.select-all-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.select-all-checkbox
  input[type="checkbox"]:indeterminate
  ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 3px;
  top: 7px;
  width: 10px;
  height: 2px;
  background-color: var(--color-white);
}

.selected-count-banner {
  background-color: rgba(74, 144, 226, 0.1);
  color: var(--color-primary);
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 13px;
  text-align: center;
}

.device-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
}

.device-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.device-item.selected {
  border-color: var(--color-primary);
  background-color: rgba(74, 144, 226, 0.05);
}

.device-item.offline {
  opacity: 0.6;
}

.device-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-danger);
  margin-right: 8px;
  flex-shrink: 0;
}

.device-status-indicator.online {
  background-color: var(--color-success);
}

.device-info {
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.device-name {
  display: flex;
  flex-direction: column;
  margin-bottom: 2px;
  width: 100%; /* 确保名称占据所有可用空间 */
}

.device-id,
.device-sn {
  font-size: 11px; /* 减小字体大小 */
  white-space: nowrap;
  /* 移除 overflow: hidden 和 text-overflow: ellipsis */
  max-width: 280px; /* 增加最大宽度，适应新的面板宽度 */
}

.device-id {
  font-weight: 500;
}

.device-sn {
  font-size: 11px;
  color: var(--color-text-secondary);
  /* 已经在上面的样式中移除了 overflow: hidden */
}

.device-status {
  font-size: 11px;
  color: var(--color-text-secondary);
}

/* 资源卡片容器样式 */
.resource-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  width: 100%;
  margin-bottom: 16px;
  align-content: start;
  box-sizing: border-box;
  min-width: 0;
}

/* 控制面板样式 */
.control-section {
  margin-bottom: 10px; /* 进一步减少底部外边距 */
  border-bottom: 1px solid var(--color-border-dark); /* 使用深色边框，与面板头部一致 */
  padding-bottom: 8px; /* 减少底部内边距 */
}

.control-section:last-child {
  border-bottom: none; /* 最后一个部分不需要分割线 */
  padding-bottom: 0;
  margin-bottom: 0; /* 最后一个部分不需要底部外边距 */
}

.control-section h5 {
  margin: 0 0 6px 0; /* 进一步减少底部外边距 */
  font-size: 13px; /* 减小字体大小 */
  color: var(--color-text-primary);
  font-weight: 600; /* 加粗标题 */
  padding-bottom: 2px; /* 减少底部内边距 */
}

.selected-resources {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 80px; /* 减少资源区域的高度 */
  overflow-y: auto;
  margin-bottom: 5px;
}

.selected-devices {
  display: flex;
  flex-direction: column;
  gap: 4px; /* 进一步减少间距，使其更紧凑 */
  max-height: 200px; /* 进一步减小最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
  margin-bottom: 5px;
}

.selected-resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  font-size: 13px;
}

.selected-device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 6px; /* 进一步减少内边距，使其更加紧凑 */
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 3px; /* 减小圆角 */
  font-size: 11px; /* 进一步减小字体大小 */
  margin-bottom: 0; /* 移除底部外边距 */
}

.btn-icon {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--color-danger);
}

/* 上部内容区域 */
.control-content {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  margin-bottom: 10px; /* 与底部操作区域的间距 */
}

/* 底部操作区域 */
.control-actions {
  display: flex;

  flex-shrink: 0; /* 防止被压缩 */
  background-color: var(--color-card-background-online);
  border-radius: var(--border-radius);
  justify-content: space-around;
  gap: 48px;
  padding: 12px 0;
  flex-direction: row;
  color: var(--color-menu-text);
  padding: 12px;
  margin-top: 8px;
}
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}
.checkbox-label input .btn-block {
  cursor: pointer;
}

.btn-publish {
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.btn-publish:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  flex: 1;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-message {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  flex-direction: column;
  min-height: 200px;
  margin-top: 25vh;
}

.empty-message span {
  margin-top: 40px;
}

/* 紧凑型空状态 - 用于控制面板 */
.empty-message-compact {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  margin-bottom: 10px;
}

/* 搜索和筛选 */
.search-input {
  padding: 6px 10px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  width: 130px;
  background-color: var(--color-card-background);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-sm);
}
.filter-select {
  margin: 0 30px 0 16px;
  cursor: pointer;
  min-width: 120px;
}

.filter-label {
  width: 60px;
  text-align: justify;
  color: var(--color-menu-text);
}

/* 下拉菜单选项样式 */
.filter-select option {
  background-color: var(--color-card-background);
  color: var(--color-text-primary);
}

/* 深色主题下的下拉菜单选项 */
[data-theme="dark"] .filter-select option {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

/* 分类过滤器样式 */
.filter-select[v-model="categoryFilter"] {
  border-color: var(--color-primary-light);
}

/* 按钮样式 */
.btn {
  padding: 7px 14px;
  border-radius: var(--border-radius-sm);
  border: none;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn i {
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark, #0c7cd5);
}

.btn-secondary {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark, #e6e6e6);
}

/* btn-text 样式已移至 DevicePanel.vue 组件 */

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 移除选择相关样式 */
.select-checkbox,
.checkbox-custom {
  display: none;
}

.serach-bar {
  display: flex;
  align-items: center;
}

.serach-bar :deep(.el-input__wrapper) {
  border-radius: 20px;
  padding-left: 15px;
  background-color: var(--color-card-background-online);
}

.serach-bar :deep(.el-input__inner) {
  color: var(--color-menu-text);
}
.serach-bar :deep(.el-input__prefix img) {
  width: 20px;
  height: 20px;
}
.dialog-content {
  width: 40vw;
  display: flex;
  padding: 0px 34px 34px;
  flex-direction: column;
  gap: 24px;
  border-radius: 16px;
  background: var(--color-card-background);

  /* 弹窗 */
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
}
.form-group {
  color: var(--color-menu-text);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.form-control {
  height: 44px;
  padding: 12px;
  align-items: center;
  gap: 13px;
  align-self: stretch;

  border: none;
  box-shadow: none;
  border-radius: 8px;
  background: var(--color-card-background-online);

  margin: 8px 0 24px;
  width: 100%;
}
/* 在全局或组件CSS中添加 */
.form-control:focus {
  border-color: #409eff; /* 蓝色边框（可替换为任意颜色值） */
  box-shadow: none;
  outline: none; /* 移除浏览器默认的outline */
}
.section-title {
  color: var(--color-menu-text);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

:deep(.el-select__wrapper) {
  background-color: #eff6fe;
  border-radius: 4.615px;
  border: 0.462px solid #1890ff;
  box-shadow: none;
}

:deep(.el-select__placeholder) {
  color: #1890ff;
  text-align: center;
}
</style>
